# 批量复权处理使用指南

## 📋 概述

批量复权处理功能提供独立的股票复权数据批量处理能力，参考下载历史数据的批量处理模式设计。支持多种复权类型和灵活的批量处理配置，充分复用项目现有的通用功能模块。

## 🚀 快速开始

### 基本使用
```bash
# 直接运行批量复权处理脚本
python data/批量复权处理.py
```

### 准备工作
1. **创建股票列表文件**（推荐）
   ```bash
   # 在数据目录下创建 stock_list.txt
   echo "000001.SZ" > data/stock_list.txt
   echo "600000.SH" >> data/stock_list.txt
   echo "000002.SZ" >> data/stock_list.txt
   ```

2. **确保原始数据存在**
   - 批量复权处理需要先有原始的股票数据
   - 可使用 `data/下载历史数据.py` 先下载原始数据

## 🎯 核心特性

### 复权类型支持
- **none**: 原始数据（不进行复权处理）
- **front**: 前复权（向前调整价格，保持最新价格不变）
- **back**: 后复权（向后调整价格，保持历史价格不变）

### 数据周期支持
- **1d**: 日线数据
- **1m**: 1分钟数据
- **5m**: 5分钟数据
- **15m**: 15分钟数据
- **30m**: 30分钟数据
- **1h**: 1小时数据
- **tick**: tick数据

### 智能配置生成
- 自动生成复权类型和数据周期的组合配置
- 支持灵活的时间范围设置
- 智能验证配置有效性

## 🔧 配置说明

### 基本配置
在 `data/批量复权处理.py` 文件中修改以下配置：

```python
# 🎯 复权类型配置
dividend_types = ["front"]  # 可配置多个: ["none", "front", "back"]

# 📊 数据周期配置
periods = ["1d", "1m"]  # 可配置多个周期

# ⏰ 时间范围配置
start_time = ""  # 开始时间，空字符串表示最早可用数据
end_time = ""    # 结束时间，空字符串表示最新数据

# 📋 显示配置
display_head_rows = 5  # 显示头部行数
display_tail_rows = 5  # 显示尾部行数
```

### 高级配置
```python
# 通用处理选项
show_data = True          # 是否显示数据预览
delay_between_configs = 2 # 配置间延时（秒）

# 股票代码来源配置
result_file = "adjustment_results.txt"  # 统一结果文件
```

## 📊 使用示例

### 示例1：前复权日线数据处理
```python
# 配置
dividend_types = ["front"]
periods = ["1d"]
start_time = "20240101"
end_time = "20241231"

# 运行
python data/批量复权处理.py
```

### 示例2：多种复权类型处理
```python
# 配置
dividend_types = ["none", "front", "back"]
periods = ["1d"]

# 运行
python data/批量复权处理.py
```

### 示例3：多周期复权处理
```python
# 配置
dividend_types = ["front"]
periods = ["1d", "1m", "5m"]

# 运行
python data/批量复权处理.py
```

## 📁 文件结构

### 输入文件
- `data/stock_list.txt` - 股票列表文件
- 原始股票数据文件（parquet格式，位于 `raw/{市场}/{代码}/{周期}/`）

### 输出文件
- `data/adjustment_results.txt` - 复权处理结果文件
- **复权数据文件**（自动保存）:
  - 前复权数据: `adjusted/front/{市场}/{代码}/{周期}/`
  - 后复权数据: `adjusted/back/{市场}/{代码}/{周期}/`
  - 原始数据: `raw/{市场}/{代码}/{周期}/`
- **复权因子文件**（自动保存）:
  - 位置: `data/dividend_factors/{代码}_{市场}_dividend_factors.parquet`
  - 包含股票的所有分红、配股等复权因子信息
- 日志文件

### 结果文件格式
```
股票复权处理结果 - 2025-01-15 15:45:00
==================================================

========== 前复权_日线数据复权处理结果 ==========
处理成功的股票:
000001.SZ
600000.SH

处理失败的股票:

未处理的股票:

无需处理的股票:

总计股票: 2
处理成功: 2
处理失败: 0
未处理: 0
无需处理: 0
最后更新时间: 2025-01-15 15:45:00
```

## 🛠️ 技术特性

### 复用项目通用功能
- **路径管理**: 使用 `config.settings.DATA_ROOT`
- **索引处理**: 使用 `IndexManager.safe_concat`
- **时间转换**: 使用 `smart_to_datetime`
- **结果管理**: 使用 `ResultManager`
- **股票代码解析**: 使用 `parse_stock_code_input`
- **复权计算**: 使用 `adjustment_synthesizer`
- **数据读取**: 使用 `read_partitioned_data_vectorized`
- **数据保存**: 使用 `ParquetStorage.save_data_by_partition_parallel`
- **复权因子管理**: 使用 `dividend_factor_storage`

### 自动数据保存功能
- **复权数据自动保存**: 处理完成后自动保存到对应的复权数据目录
- **复权因子自动获取**: 从xtquant自动获取并保存复权因子数据
- **增量更新**: 复权因子支持增量更新，避免重复下载
- **数据验证**: 保存后自动验证数据格式和完整性
- **状态跟踪**: 实时跟踪数据保存状态和结果

### 性能优化
- 智能缓存机制
- 向量化数据处理
- 批量处理优化
- 内存使用优化

### 错误处理
- 完善的异常处理机制
- 详细的错误日志记录
- 处理结果状态跟踪
- 失败重试机制

## 📈 处理流程

1. **配置验证**: 验证复权类型和数据周期配置
2. **股票列表读取**: 从文件读取要处理的股票列表
3. **复权因子获取**: 自动从xtquant获取并保存复权因子数据
4. **数据读取**: 读取原始股票数据
5. **复权计算**: 使用复权因子计算调整后价格
6. **索引验证**: 验证和修复数据索引格式
7. **数据保存**: 自动保存复权数据到parquet文件
8. **结果记录**: 保存处理结果和状态到结果文件
9. **状态验证**: 验证数据保存完整性
10. **统计报告**: 生成处理统计和数据保存报告

## ⚠️ 注意事项

1. **数据依赖**: 需要先有原始股票数据才能进行复权处理
2. **内存使用**: 大量数据处理时注意内存使用情况
3. **时间范围**: 合理设置时间范围避免处理过多数据
4. **配置验证**: 确保复权类型和周期配置正确
5. **索引格式**: 遵循项目索引格式标准，使用IndexManager进行安全操作

## 🔍 故障排除

### 常见问题

#### 1. 没有可用数据
**症状**: 提示"没有可用的数据"
**解决方案**: 
- 检查原始数据是否存在
- 使用下载历史数据功能先下载数据
- 检查股票代码格式是否正确

#### 2. 复权处理失败
**症状**: 复权计算返回失败
**解决方案**:
- 检查复权因子数据是否完整
- 验证数据时间范围设置
- 查看详细错误日志

#### 3. 索引格式错误
**症状**: 索引格式验证失败
**解决方案**:
- 使用IndexManager自动修复
- 检查原始数据索引格式
- 参考索引格式处理指南

#### 4. 数据保存失败
**症状**: 提示"parquet数据保存失败"
**解决方案**:
- 检查磁盘空间是否充足
- 验证数据目录写入权限
- 检查数据格式是否正确
- 查看详细错误日志

#### 5. 复权因子获取失败
**症状**: 提示"复权因子数据更新失败"
**解决方案**:
- 检查xtquant连接状态
- 验证股票代码格式
- 检查网络连接
- 尝试手动更新复权因子

## 📚 相关文档

- [复权功能用户指南](复权功能用户指南.md)
- [索引格式处理指南](索引格式处理指南.md)
- [下载历史数据使用指南](README_下载历史数据.md)
- [项目常见问题](../项目常见问题.md)

## 🎯 最佳实践

1. **分批处理**: 对于大量股票，建议分批处理，避免内存占用过高
2. **时间设置**: 合理设置时间范围，避免处理不必要的数据
3. **配置管理**: 使用配置文件管理复杂的处理配置
4. **结果验证**: 处理完成后验证结果数据的正确性
5. **日志监控**: 关注处理日志，及时发现和解决问题
6. **数据备份**: 处理前备份重要的原始数据
7. **磁盘空间**: 确保有足够的磁盘空间存储复权数据
8. **复权因子更新**: 定期更新复权因子数据，确保数据准确性
9. **网络稳定**: 确保网络连接稳定，避免复权因子获取失败
10. **权限检查**: 确保对数据目录有读写权限
