#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批量复权处理模块

参考下载历史数据的批量处理模式，提供独立的批量复权处理功能
支持多种复权类型和批量处理配置
"""

import os
import sys
import datetime
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 复用项目现有的通用功能模块
from config.settings import DATA_ROOT
from utils.logger import get_unified_logger, LogTarget
from utils.data_processor.index_manager import IndexManager
from utils.smart_time_converter import smart_to_datetime
from data.core.result_manager import ResultManager
from utils.text_parser import parse_stock_code_input
from utils.data_processor.adjustment import adjustment_synthesizer, dividend_factor_storage
from data.storage.vectorized_reader import read_partitioned_data_vectorized
from data.storage.parquet_storage import ParquetStorage

logger = get_unified_logger(__name__)


def _create_initial_adjustment_result_file(stock_list_file: str, result_file_path: str):
    """从股票列表文件创建初始复权处理结果文件"""
    try:
        # 读取股票列表
        with open(stock_list_file, 'r', encoding='utf-8') as f:
            stock_lines = f.readlines()

        # 解析股票代码 - 使用专业文本解析工具处理带注释的代码
        stocks = []
        for line in stock_lines:
            line = line.strip()
            if line and not line.startswith('#'):
                # 使用专业解析函数处理可能包含注释的股票代码
                parsed_codes = parse_stock_code_input(line)
                stocks.extend(parsed_codes)

        # 定义支持的复权类型
        adjustment_types = [
            {"code": "none", "name": "原始数据"},
            {"code": "front", "name": "前复权"},
            {"code": "back", "name": "后复权"}
        ]

        # 定义支持的数据周期
        periods = [
            {"code": "1d", "name": "日线"},
            {"code": "1m", "name": "1分钟"},
            {"code": "tick", "name": "tick"}
        ]

        # 创建复权处理初始结果文件
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        with open(result_file_path, 'w', encoding='utf-8') as f:
            f.write(f"股票复权处理结果 - {timestamp}\n")
            f.write("="*50 + "\n\n")

            # 为每个复权类型和周期组合创建初始的处理结果分段
            for adj_type in adjustment_types:
                for period in periods:
                    section_name = f"{adj_type['name']}_{period['name']}"
                    f.write(f"========== {section_name}数据复权处理结果 ==========\n")

                    # 写入各部分（初始状态：所有股票都是未处理）
                    sections = [
                        ("处理成功的股票:", []),
                        ("处理失败的股票:", []),
                        ("未处理的股票:", sorted(stocks)),
                        ("无需处理的股票:", [])
                    ]

                    for title, stock_list in sections:
                        f.write(f"{title}\n")
                        for stock in stock_list:
                            f.write(f"{stock}\n")
                        f.write("\n")

                    # 写入统计信息
                    total = len(stocks)
                    f.write(f"总计股票: {total}\n")
                    f.write(f"处理成功: 0\n")
                    f.write(f"处理失败: 0\n")
                    f.write(f"未处理: {total}\n")
                    f.write(f"无需处理: 0\n")
                    f.write(f"最后更新时间: {timestamp}\n\n")

        print(f"✅ 已创建复权处理初始结果文件，包含 {len(stocks)} 只股票")
        print(f"📊 已为 {len(adjustment_types)} 种复权类型 × {len(periods)} 个周期初始化处理状态")

    except Exception as e:
        print(f"❌ 创建初始复权处理结果文件失败: {e}")


def _read_stock_list_from_file(stock_list_file: str) -> list:
    """从文件读取股票列表"""
    try:
        if not os.path.exists(stock_list_file):
            print(f"❌ 股票列表文件不存在: {stock_list_file}")
            return []

        with open(stock_list_file, 'r', encoding='utf-8') as f:
            stock_lines = f.readlines()

        # 解析股票代码 - 使用专业文本解析工具处理带注释的代码
        stocks = []
        for line in stock_lines:
            line = line.strip()
            if line and not line.startswith('#'):
                # 使用专业解析函数处理可能包含注释的股票代码
                parsed_codes = parse_stock_code_input(line)
                stocks.extend(parsed_codes)

        print(f"📋 从文件读取到 {len(stocks)} 只股票")
        return stocks

    except Exception as e:
        print(f"❌ 读取股票列表文件失败: {e}")
        return []


def _get_default_stock_list():
    """获取默认股票列表"""
    return ['000001.SZ']  # 默认使用平安银行作为示例


def save_adjustment_result(symbol: str, period: str, dividend_type: str,
                         result: dict, result_file_path: str):
    """保存复权处理结果到结果文件和parquet数据文件

    Args:
        symbol: 股票代码
        period: 数据周期
        dividend_type: 复权类型
        result: 处理结果
        result_file_path: 结果文件路径
    """
    try:
        # 1. 使用ResultManager来管理结果文件
        result_manager = ResultManager(result_file=result_file_path)

        # 2. 如果处理成功且有数据，保存复权数据到parquet文件
        if result["success"] and result.get("data") is not None:
            adjusted_data = result["data"]

            # 确定数据类型和复权类型参数
            if dividend_type == "none":
                data_type = "raw"
                adj_type = None
            else:
                data_type = "adjusted"
                adj_type = dividend_type  # "front" 或 "back"

            # 使用ParquetStorage保存复权数据
            storage = ParquetStorage(base_dir=DATA_ROOT)
            save_success = storage.save_data_by_partition_parallel(
                dataframe=adjusted_data,
                symbol=symbol,
                period=period,
                data_type=data_type,
                adj_type=adj_type,
                num_workers=4  # 使用4个并行工作进程
            )

            if save_success:
                logger.info(LogTarget.FILE, f"✅ {symbol} {dividend_type}复权数据保存成功: {len(adjusted_data)}行")

                # 记录保存路径信息
                if data_type == "adjusted":
                    save_path = f"adjusted/{adj_type}/{symbol.split('.')[1]}/{symbol.split('.')[0]}/{period}/"
                else:
                    save_path = f"raw/{symbol.split('.')[1]}/{symbol.split('.')[0]}/{period}/"
                logger.debug(LogTarget.FILE, f"数据保存路径: {save_path}")

                # 验证保存后的数据（可选的完整性检查）
                try:
                    # 验证索引格式
                    if not IndexManager.validate_index_format(adjusted_data):
                        logger.warning(LogTarget.FILE, f"⚠️ {symbol} 保存的数据索引格式可能存在问题")

                    # 更新结果状态
                    result["data_save_success"] = True
                    result["save_path"] = save_path
                    result["saved_rows"] = len(adjusted_data)

                except Exception as verify_e:
                    logger.warning(LogTarget.FILE, f"⚠️ {symbol} 数据保存后验证失败: {verify_e}")

            else:
                logger.error(LogTarget.FILE, f"❌ {symbol} {dividend_type}复权数据保存失败")
                # 更新结果状态
                result["data_save_success"] = False
                result["data_save_error"] = "parquet数据保存失败"
        else:
            if result["success"]:
                logger.warning(LogTarget.FILE, f"⚠️ {symbol} 处理成功但无数据可保存")
            else:
                logger.warning(LogTarget.FILE, f"❌ {symbol} 处理失败，跳过数据保存: {result.get('error', '未知错误')}")

        # 3. 记录处理结果到日志
        if result["success"]:
            logger.info(LogTarget.FILE, f"保存成功结果: {symbol} {dividend_type} {period}：\n{adjusted_data}")
        else:
            logger.warning(LogTarget.FILE, f"保存失败结果: {symbol} {dividend_type} {period} - {result.get('error', '未知错误')}")

    except Exception as e:
        logger.error(LogTarget.FILE, f"保存复权处理结果失败: {e}", exc_info=True)


def process_single_stock_adjustment(symbol: str, period: str, dividend_type: str,
                                  start_time: str = "", end_time: str = "",
                                  display_head_rows: int = 5, display_tail_rows: int = 5,
                                  show_data: bool = True, save_result: bool = False,
                                  result_file_path: str = "") -> dict:
    """处理单只股票的复权数据

    Args:
        symbol: 股票代码
        period: 数据周期
        dividend_type: 复权类型 (none/front/back)
        start_time: 开始时间
        end_time: 结束时间
        display_head_rows: 显示头部行数
        display_tail_rows: 显示尾部行数
        show_data: 是否显示数据
        save_result: 是否保存结果到文件
        result_file_path: 结果文件路径

    Returns:
        dict: 处理结果
    """
    try:
        logger.info(LogTarget.FILE, f"开始处理股票 {symbol} 的 {dividend_type} 复权数据，周期: {period}")

        # 1. 如果需要复权处理，先确保复权因子数据可用
        if dividend_type != "none":
            logger.info(LogTarget.FILE, f"检查并更新股票 {symbol} 的复权因子数据")
            try:
                # 使用dividend_factor_storage自动获取和保存复权因子
                factor_update_success = dividend_factor_storage.update_dividend_factors(
                    stock_code=symbol,
                    force_update=False,  # 增量更新，如果本地没有数据会自动获取全部
                    start_time=start_time,
                    end_time=end_time
                )

                if factor_update_success:
                    logger.info(LogTarget.FILE, f"✅ {symbol} 复权因子数据更新成功")
                else:
                    logger.warning(LogTarget.FILE, f"⚠️ {symbol} 复权因子数据更新失败，将尝试继续复权处理")

            except Exception as factor_e:
                logger.error(LogTarget.FILE, f"❌ {symbol} 复权因子处理异常: {factor_e}")
                logger.warning(LogTarget.FILE, f"将尝试继续复权处理，可能使用缓存的复权因子数据")

        # 2. 读取原始数据
        raw_data = read_partitioned_data_vectorized(
            data_root=DATA_ROOT,
            symbol=symbol,
            period=period,
            start_time=start_time,
            end_time=end_time,
            dividend_type="none"  # 先读取原始数据
        )
        
        if raw_data is None or raw_data.empty:
            logger.warning(LogTarget.FILE, f"股票 {symbol} 没有可用的 {period} 数据")
            return {
                "success": False,
                "symbol": symbol,
                "period": period,
                "dividend_type": dividend_type,
                "error": "没有可用数据",
                "data": None
            }
        
        # 如果是原始数据，直接返回
        if dividend_type == "none":
            adjusted_data = raw_data
        else:
            # 进行复权处理
            adjusted_data = adjustment_synthesizer.synthesize_adjusted_data(
                symbol=symbol,
                price_data=raw_data,
                dividend_type=dividend_type,
                method="ratio",  # 使用等比复权
                use_cache=True
            )
            
            if adjusted_data is None:
                logger.error(LogTarget.FILE, f"股票 {symbol} 复权处理失败")
                return {
                    "success": False,
                    "symbol": symbol,
                    "period": period,
                    "dividend_type": dividend_type,
                    "error": "复权处理失败",
                    "data": None
                }
        
        # 验证索引格式
        if not IndexManager.validate_index_format(adjusted_data):
            logger.warning(LogTarget.FILE, f"股票 {symbol} 复权后索引格式不正确，尝试修复")
            adjusted_data = IndexManager.ensure_proper_index(adjusted_data)
        
        logger.info(LogTarget.FILE, f"股票 {symbol} 复权处理成功，数据行数: {len(adjusted_data)}\n: {adjusted_data}")

        result = {
            "success": True,
            "symbol": symbol,
            "period": period,
            "dividend_type": dividend_type,
            "data": adjusted_data,
            "rows": len(adjusted_data)
        }

        # 保存处理结果
        if save_result and result_file_path:
            save_adjustment_result(symbol, period, dividend_type, result, result_file_path)

        return result

    except Exception as e:
        logger.error(LogTarget.FILE, f"股票 {symbol} 复权处理异常: {e}", exc_info=True)

        result = {
            "success": False,
            "symbol": symbol,
            "period": period,
            "dividend_type": dividend_type,
            "error": str(e),
            "data": None
        }

        # 保存处理结果（即使失败也要记录）
        if save_result and result_file_path:
            save_adjustment_result(symbol, period, dividend_type, result, result_file_path)

        return result


def generate_adjustment_configs(dividend_types: list, periods: list,
                             start_time: str = "", end_time: str = "",
                             display_head_rows: int = 5, display_tail_rows: int = 5) -> list:
    """
    根据复权类型和周期列表自动生成复权处理配置

    Args:
        dividend_types: 复权类型列表，如 ['none', 'front', 'back']
        periods: 数据周期列表，如 ['1d', '1m', 'tick']
        start_time: 开始时间
        end_time: 结束时间
        display_head_rows: 显示头部行数
        display_tail_rows: 显示尾部行数

    Returns:
        List[Dict]: 生成的复权处理配置列表
    """
    logger.info(LogTarget.FILE, f"开始生成复权处理配置，复权类型: {dividend_types}, 周期: {periods}")

    # 复权类型映射
    dividend_type_names = {
        "none": "原始数据",
        "front": "前复权",
        "back": "后复权"
    }

    # 周期名称映射
    period_names = {
        "1d": "日线",
        "1m": "1分钟",
        "5m": "5分钟",
        "15m": "15分钟",
        "30m": "30分钟",
        "1h": "1小时",
        "tick": "tick"
    }

    configs = []
    valid_configs = []
    invalid_configs = []

    for dividend_type in dividend_types:
        if dividend_type not in dividend_type_names:
            invalid_configs.append(f"无效复权类型: {dividend_type}")
            continue

        for period in periods:
            if period not in period_names:
                invalid_configs.append(f"无效周期: {period}")
                continue

            config = {
                "dividend_type": dividend_type,
                "dividend_name": dividend_type_names[dividend_type],
                "period": period,
                "period_name": period_names[period],
                "start_time": start_time,
                "end_time": end_time,
                "display_head_rows": display_head_rows,
                "display_tail_rows": display_tail_rows,
                "config_name": f"{dividend_type_names[dividend_type]}_{period_names[period]}"
            }

            configs.append(config)
            valid_configs.append(config["config_name"])

    # 记录统计信息
    logger.info(LogTarget.FILE, f"配置生成完成: 有效配置 {len(valid_configs)} 个, 无效配置 {len(invalid_configs)} 个")
    if invalid_configs:
        logger.warning(LogTarget.FILE, f"无效配置列表: {invalid_configs}")

    return configs


def main():
    """主函数"""
    # ==================== 复权处理配置 ====================

    # 🎯 复权类型配置：选择要处理的复权类型
    #
    # 支持的复权类型：
    # - "none": 原始数据（不进行复权处理）
    # - "front": 前复权（向前调整价格，保持最新价格不变）
    # - "back": 后复权（向后调整价格，保持历史价格不变）
    #
    # 推荐设置：
    # - 技术分析：使用 "front" 前复权
    # - 历史回测：使用 "front" 前复权
    # - 原始数据分析：使用 "none" 不复权
    dividend_types = ["front"]  # 可配置多个: ["none", "front", "back"]

    # 📊 数据周期配置：选择要处理的数据周期
    #
    # 支持的周期：
    # - "1d": 日线数据
    # - "1m": 1分钟数据
    # - "5m": 5分钟数据
    # - "15m": 15分钟数据
    # - "30m": 30分钟数据
    # - "1h": 1小时数据
    # - "tick": tick数据
    periods = ["tick"]  # 可配置多个周期

    # ⏰ 时间范围配置
    start_time = "20250715145500"  # 开始时间，空字符串表示最早可用数据
    end_time = "20250716093500"    # 结束时间，空字符串表示最新数据

    # 📋 显示配置
    display_head_rows = 5  # 显示头部行数
    display_tail_rows = 5  # 显示尾部行数

    # 生成复权处理配置
    adjustment_configs = generate_adjustment_configs(
        dividend_types=dividend_types,
        periods=periods,
        start_time=start_time,
        end_time=end_time,
        display_head_rows=display_head_rows,
        display_tail_rows=display_tail_rows
    )
    
    # 通用处理选项
    show_data = True          # 是否显示数据预览
    delay_between_configs = 2 # 配置间延时（秒）
    
    # 股票代码来源配置
    result_file = "adjustment_results.txt"  # 统一结果文件
    
    # ==================== 开始批量复权处理 ====================
    
    total_configs = len(adjustment_configs)
    print(f"\n🚀 开始批量复权处理，共 {total_configs} 个配置")
    
    # 显示结果文件路径信息
    result_file_path = os.path.join(DATA_ROOT, result_file)
    print(f"📋 统一结果文件: {result_file_path}")
    
    # 检查并创建初始结果文件
    if not os.path.exists(result_file_path):
        # 尝试从stock_list.txt创建初始结果文件
        stock_list_file = os.path.join(DATA_ROOT, "stock_list.txt")
        if os.path.exists(stock_list_file):
            print(f"📋 从股票列表文件创建初始结果文件: {stock_list_file}")
            _create_initial_adjustment_result_file(stock_list_file, result_file_path)
        else:
            print(f"❌ 结果文件和股票列表文件都不存在")
            print(f"请创建 {stock_list_file} 文件并添加股票代码，或直接创建 {result_file_path}")
            return
    
    # 读取股票列表
    stock_list = _read_stock_list_from_file(os.path.join(DATA_ROOT, "stock_list.txt"))
    if not stock_list:
        stock_list = _get_default_stock_list()
        print(f"📋 使用默认股票列表: {stock_list}")
    
    print(f"📊 将处理 {len(stock_list)} 只股票")

    # 多配置处理循环
    overall_stats = {
        "total_configs": total_configs,
        "successful_configs": 0,
        "failed_configs": 0,
        "total_stocks_processed": 0,
        "total_processing_time": 0,
        "config_results": []
    }

    for i, config in enumerate(adjustment_configs, 1):
        dividend_type = config["dividend_type"]
        dividend_name = config["dividend_name"]
        period = config["period"]
        period_name = config["period_name"]
        start_time = config["start_time"]
        end_time = config["end_time"]
        display_head_rows = config["display_head_rows"]
        display_tail_rows = config["display_tail_rows"]

        config_name = f"{dividend_name}_{period_name}"

        print(f"\n{'='*60}")
        print(f"📈 [{i}/{total_configs}] 开始处理 {config_name} 数据...")
        print(f"⚙️ 复权类型: {dividend_type}")
        print(f"📊 数据周期: {period}")
        print(f"⏰ 时间范围: {start_time or '最早可用'} ~ {end_time or '今天'}")
        print(f"📁 结果文件: {result_file}")
        print(f"{'='*60}")

        config_start_time = time.time()
        config_success = True
        stocks_processed = 0

        # 处理每只股票
        for j, symbol in enumerate(stock_list, 1):
            print(f"\n📈 [{j}/{len(stock_list)}] 处理股票: {symbol}")

            # 处理单只股票的复权数据
            result = process_single_stock_adjustment(
                symbol=symbol,
                period=period,
                dividend_type=dividend_type,
                start_time=start_time,
                end_time=end_time,
                display_head_rows=display_head_rows,
                display_tail_rows=display_tail_rows,
                show_data=show_data,
                save_result=True,  # 启用结果保存
                result_file_path=result_file_path
            )

            if result["success"]:
                # 检查数据保存状态
                data_save_status = result.get("data_save_success", True)  # 默认认为成功
                save_info = ""

                if data_save_status:
                    saved_rows = result.get("saved_rows", result.get('rows', 0))
                    save_path = result.get("save_path", "")
                    save_info = f"，已保存{saved_rows}行到{save_path}"
                else:
                    save_error = result.get("data_save_error", "未知错误")
                    save_info = f"，数据保存失败: {save_error}"
                    logger.warning(LogTarget.FILE, f"{symbol} 复权处理成功但数据保存失败: {save_error}")

                print(f"✅ {symbol} 复权处理成功，数据行数: {result['rows']}{save_info}")
                stocks_processed += 1
            else:
                print(f"❌ {symbol} 复权处理失败: {result['error']}")
                config_success = False

        config_duration = time.time() - config_start_time

        # 记录配置处理结果
        config_result = {
            "config_name": config_name,
            "success": config_success,
            "stocks_processed": stocks_processed,
            "duration": config_duration
        }
        overall_stats["config_results"].append(config_result)

        if config_success:
            print(f"✅ {config_name} 处理完成，耗时: {config_duration:.2f}秒")
            overall_stats["successful_configs"] += 1
            logger.info(LogTarget.FILE, f"{config_name} 处理完成")
        else:
            print(f"❌ {config_name} 处理失败，耗时: {config_duration:.2f}秒")
            overall_stats["failed_configs"] += 1
            logger.error(LogTarget.FILE, f"{config_name} 处理失败")

        overall_stats["total_stocks_processed"] += stocks_processed
        overall_stats["total_processing_time"] += config_duration

        # 添加延时，避免请求过于频繁
        if i < total_configs:  # 最后一个配置不需要延时
            print(f"⏳ 等待{delay_between_configs}秒后继续处理下一个配置...")
            time.sleep(delay_between_configs)

    # 处理完成总结
    print(f"\n🎉 {'='*60}")
    print(f"🎉 所有复权处理完成！")
    print(f"📊 处理统计:")
    print(f"   - 总配置数: {overall_stats['total_configs']}")
    print(f"   - 成功配置: {overall_stats['successful_configs']}")
    print(f"   - 失败配置: {overall_stats['failed_configs']}")
    print(f"   - 总处理股票数: {overall_stats['total_stocks_processed']}")
    print(f"   - 总耗时: {overall_stats['total_processing_time']:.2f}秒")

    # 数据保存统计
    print(f"\n💾 数据保存说明:")
    print(f"   - 复权数据已保存到: {DATA_ROOT}/adjusted/{{复权类型}}/{{市场}}/{{代码}}/{{周期}}/")
    print(f"   - 复权因子已保存到: {DATA_ROOT}/data/dividend_factors/")
    print(f"   - 原始数据路径: {DATA_ROOT}/raw/{{市场}}/{{代码}}/{{周期}}/")
    print(f"   - 详细保存日志请查看日志文件")

    # 各配置详细结果
    print(f"\n📋 各配置详细结果:")
    for config_result in overall_stats["config_results"]:
        status = "✅" if config_result["success"] else "❌"
        print(f"   {status} {config_result['config_name']}: {config_result['duration']:.2f}秒, {config_result['stocks_processed']}只股票")

    # 配置信息
    print(f"\n📅 各配置详细信息:")
    for config in adjustment_configs:
        start_time = config['start_time'] or "最早可用"
        end_time = config['end_time'] or "今天"
        print(f"   - {config['dividend_name']}_{config['period_name']}: {start_time} ~ {end_time}")

    print(f"\n🎉 {'='*60}")
    print(f"🎉 批量复权处理任务全部完成！")
    print(f"📋 详细日志请查看日志文件")
    print(f"🎉 {'='*60}")


if __name__ == "__main__":
    main()
